# Flutter Widget 教学项目

一个全面的Flutter Widget展示和学习应用，专为学习Flutter开发而设计。

## 项目简介

这个项目是一个完整的Flutter教学应用，包含了Flutter中各种常用Widget的详细示例和中文注释。每个Widget都有详细的属性说明，帮助初学者更好地理解Flutter开发。

## 功能特色

- 📱 **完整的Widget展示** - 涵盖基础、布局、交互、滚动等各类Widget
- 📝 **详细的中文注释** - 每个属性都有详细的中文说明
- 🎨 **美观的界面设计** - 使用Material 3设计规范
- 🔍 **分类清晰** - 按功能分类，便于学习和查找
- 💡 **实用的示例** - 每个Widget都有实际的使用示例

## 项目结构

```
lib/
├── main.dart                          # 应用入口和主页面
└── widgets/                           # Widget展示页面
    ├── basic_widgets_page.dart        # 基础组件展示
    ├── layout_widgets_page.dart       # 布局组件展示
    ├── interactive_widgets_page.dart  # 交互组件展示
    └── scrollable_widgets_page.dart   # 滚动组件展示
```

## Widget分类

### 1. 基础组件 (Basic Widgets)
- **Text** - 文本显示组件
- **Container** - 容器组件，提供装饰、定位、尺寸约束
- **Icon** - 图标组件
- **Image** - 图片显示组件

### 2. 布局组件 (Layout Widgets)
- **Row** - 水平布局组件
- **Column** - 垂直布局组件
- **Stack** - 层叠布局组件
- **Flex** - 弹性布局组件
- **Wrap** - 流式布局组件

### 3. 交互组件 (Interactive Widgets)
- **Button系列** - ElevatedButton、OutlinedButton、TextButton、IconButton
- **TextField** - 文本输入框
- **Switch** - 开关组件
- **Checkbox** - 复选框组件
- **Radio** - 单选按钮组件
- **Slider** - 滑块组件

### 4. 滚动组件 (Scrollable Widgets)
- **ListView** - 列表视图
- **GridView** - 网格视图
- **PageView** - 页面视图
- **SingleChildScrollView** - 单子组件滚动视图
- **CustomScrollView** - 自定义滚动视图

## 运行项目

1. 确保已安装Flutter SDK
2. 克隆或下载项目到本地
3. 在项目根目录运行：
   ```bash
   flutter pub get
   flutter run
   ```

## 学习建议

1. **从基础开始** - 先学习基础组件，理解Flutter的基本概念
2. **动手实践** - 运行项目，查看每个Widget的实际效果
3. **阅读注释** - 仔细阅读代码中的中文注释，理解每个属性的作用
4. **修改尝试** - 尝试修改代码中的参数，观察界面变化
5. **举一反三** - 基于示例代码，尝试创建自己的Widget组合

## 贡献

欢迎提交Issue和Pull Request来完善这个教学项目！

## 许可证

本项目采用MIT许可证。
