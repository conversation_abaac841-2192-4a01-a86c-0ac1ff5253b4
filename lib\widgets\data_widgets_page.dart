import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库
import 'package:table_calendar/table_calendar.dart'; // 导入日历组件库

// 数据展示组件页面
class DataWidgetsPage extends StatefulWidget {
  const DataWidgetsPage({super.key}); // 构造函数，接收可选的key参数

  @override
  State<DataWidgetsPage> createState() => _DataWidgetsPageState();
}

class _DataWidgetsPageState extends State<DataWidgetsPage> {
  // 表单相关状态
  final _formKey = GlobalKey<FormState>(); // 表单键
  final _nameController = TextEditingController(); // 姓名输入控制器
  final _emailController = TextEditingController(); // 邮箱输入控制器
  final _phoneController = TextEditingController(); // 电话输入控制器
  String _selectedGender = '男'; // 选中的性别
  bool _agreeTerms = false; // 是否同意条款

  // 日历相关状态
  DateTime _focusedDay = DateTime.now(); // 当前焦点日期
  DateTime? _selectedDay; // 选中的日期
  CalendarFormat _calendarFormat = CalendarFormat.month; // 日历格式

  // DataTable数据
  final List<Map<String, dynamic>> _tableData = [
    {
      'id': 1,
      'name': '张三',
      'age': 25,
      'department': '技术部',
      'salary': 8000,
      'selected': false,
    },
    {
      'id': 2,
      'name': '李四',
      'age': 30,
      'department': '设计部',
      'salary': 7500,
      'selected': false,
    },
    {
      'id': 3,
      'name': '王五',
      'age': 28,
      'department': '产品部',
      'salary': 9000,
      'selected': false,
    },
    {
      'id': 4,
      'name': '赵六',
      'age': 32,
      'department': '运营部',
      'salary': 7000,
      'selected': false,
    },
    {
      'id': 5,
      'name': '钱七',
      'age': 26,
      'department': '技术部',
      'salary': 8500,
      'selected': false,
    },
  ];

  @override
  void dispose() {
    _nameController.dispose(); // 释放控制器资源
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('数据展示组件'), // AppBar标题
        backgroundColor:
            Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // DataTable 数据表格示例
          _buildDataSection(
            context,
            'DataTable - 数据表格',
            '可排序、可选择的数据表格组件',
            SingleChildScrollView(
              scrollDirection: Axis.horizontal, // 水平滚动
              child: DataTable(
                sortAscending: true, // 升序排序
                columns: const [
                  DataColumn(
                    label: Text(
                      'ID',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ), // 列标题
                    numeric: true, // 数字列
                  ),
                  DataColumn(
                    label: Text(
                      '姓名',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ), // 列标题
                  ),
                  DataColumn(
                    label: Text(
                      '年龄',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ), // 列标题
                    numeric: true, // 数字列
                  ),
                  DataColumn(
                    label: Text(
                      '部门',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ), // 列标题
                  ),
                  DataColumn(
                    label: Text(
                      '薪资',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ), // 列标题
                    numeric: true, // 数字列
                  ),
                ],
                rows:
                    _tableData.map((data) {
                      return DataRow(
                        selected: data['selected'], // 是否选中
                        onSelectChanged: (bool? selected) {
                          setState(() {
                            data['selected'] = selected ?? false; // 更新选中状态
                          });
                        },
                        cells: [
                          DataCell(Text(data['id'].toString())), // ID单元格
                          DataCell(Text(data['name'])), // 姓名单元格
                          DataCell(Text(data['age'].toString())), // 年龄单元格
                          DataCell(Text(data['department'])), // 部门单元格
                          DataCell(Text('¥${data['salary']}')), // 薪资单元格
                        ],
                      );
                    }).toList(),
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // Calendar 日历组件示例
          _buildDataSection(
            context,
            'Calendar - 日历组件',
            '功能丰富的日历选择组件',
            Column(
              children: [
                TableCalendar<String>(
                  firstDay: DateTime.utc(2020, 1, 1), // 最早日期
                  lastDay: DateTime.utc(2030, 12, 31), // 最晚日期
                  focusedDay: _focusedDay, // 当前焦点日期
                  calendarFormat: _calendarFormat, // 日历格式
                  selectedDayPredicate: (day) {
                    return isSameDay(_selectedDay, day); // 判断是否为选中日期
                  },
                  onDaySelected: (selectedDay, focusedDay) {
                    setState(() {
                      _selectedDay = selectedDay; // 更新选中日期
                      _focusedDay = focusedDay; // 更新焦点日期
                    });
                  },
                  onFormatChanged: (format) {
                    setState(() {
                      _calendarFormat = format; // 更新日历格式
                    });
                  },
                  onPageChanged: (focusedDay) {
                    _focusedDay = focusedDay; // 更新焦点日期
                  },
                  calendarStyle: const CalendarStyle(
                    outsideDaysVisible: false, // 不显示外部日期
                    weekendTextStyle: TextStyle(color: Colors.red), // 周末文字样式
                    holidayTextStyle: TextStyle(color: Colors.red), // 节假日文字样式
                  ),
                  headerStyle: const HeaderStyle(
                    formatButtonVisible: true, // 显示格式按钮
                    titleCentered: true, // 标题居中
                    formatButtonShowsNext: false, // 格式按钮不显示下一个
                  ),
                ),
                const SizedBox(height: 16), // 垂直间距16像素
                if (_selectedDay != null)
                  Text(
                    '选中日期: ${_selectedDay!.year}-${_selectedDay!.month.toString().padLeft(2, '0')}-${_selectedDay!.day.toString().padLeft(2, '0')}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // Form 表单组件示例
          _buildDataSection(
            context,
            'Form - 表单组件',
            '完整的表单验证和提交功能',
            Form(
              key: _formKey, // 表单键
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                children: [
                  // 姓名输入框
                  TextFormField(
                    controller: _nameController, // 绑定控制器
                    decoration: const InputDecoration(
                      labelText: '姓名', // 标签文本
                      hintText: '请输入您的姓名', // 提示文本
                      prefixIcon: Icon(Icons.person), // 前缀图标
                      border: OutlineInputBorder(), // 边框样式
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入姓名'; // 验证失败提示
                      }
                      if (value.length < 2) {
                        return '姓名至少2个字符'; // 验证失败提示
                      }
                      return null; // 验证通过
                    },
                  ),
                  const SizedBox(height: 16), // 垂直间距16像素
                  // 邮箱输入框
                  TextFormField(
                    controller: _emailController, // 绑定控制器
                    keyboardType: TextInputType.emailAddress, // 邮箱键盘类型
                    decoration: const InputDecoration(
                      labelText: '邮箱', // 标签文本
                      hintText: '请输入您的邮箱', // 提示文本
                      prefixIcon: Icon(Icons.email), // 前缀图标
                      border: OutlineInputBorder(), // 边框样式
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入邮箱'; // 验证失败提示
                      }
                      if (!RegExp(
                        r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                      ).hasMatch(value)) {
                        return '请输入有效的邮箱地址'; // 验证失败提示
                      }
                      return null; // 验证通过
                    },
                  ),
                  const SizedBox(height: 16), // 垂直间距16像素
                  // 电话输入框
                  TextFormField(
                    controller: _phoneController, // 绑定控制器
                    keyboardType: TextInputType.phone, // 电话键盘类型
                    decoration: const InputDecoration(
                      labelText: '电话', // 标签文本
                      hintText: '请输入您的电话号码', // 提示文本
                      prefixIcon: Icon(Icons.phone), // 前缀图标
                      border: OutlineInputBorder(), // 边框样式
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入电话号码'; // 验证失败提示
                      }
                      if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                        return '请输入有效的手机号码'; // 验证失败提示
                      }
                      return null; // 验证通过
                    },
                  ),
                  const SizedBox(height: 16), // 垂直间距16像素
                  // 性别选择
                  const Text(
                    '性别:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<String>(
                          title: const Text('男'), // 单选项标题
                          value: '男', // 单选项值
                          groupValue: _selectedGender, // 当前选中值
                          onChanged: (String? value) {
                            setState(() {
                              _selectedGender = value!; // 更新选中值
                            });
                          },
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<String>(
                          title: const Text('女'), // 单选项标题
                          value: '女', // 单选项值
                          groupValue: _selectedGender, // 当前选中值
                          onChanged: (String? value) {
                            setState(() {
                              _selectedGender = value!; // 更新选中值
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16), // 垂直间距16像素
                  // 同意条款复选框
                  CheckboxListTile(
                    title: const Text('我同意用户协议和隐私政策'), // 复选框标题
                    value: _agreeTerms, // 复选框当前值
                    onChanged: (bool? value) {
                      setState(() {
                        _agreeTerms = value ?? false; // 更新复选框状态
                      });
                    },
                    controlAffinity: ListTileControlAffinity.leading, // 控件在前
                  ),
                  const SizedBox(height: 24), // 垂直间距24像素
                  // 提交按钮
                  SizedBox(
                    width: double.infinity, // 宽度填满父容器
                    child: ElevatedButton(
                      onPressed: _agreeTerms ? _submitForm : null, // 只有同意条款才能提交
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          vertical: 16,
                        ), // 垂直内边距16像素
                      ),
                      child: const Text(
                        '提交表单',
                        style: TextStyle(fontSize: 16),
                      ), // 按钮文本
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 提交表单的方法
  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      // 表单验证通过
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '表单提交成功！\n姓名: ${_nameController.text}\n邮箱: ${_emailController.text}\n电话: ${_phoneController.text}\n性别: $_selectedGender',
          ),
          duration: const Duration(seconds: 3), // 显示时长3秒
        ),
      );

      // 清空表单
      _nameController.clear();
      _emailController.clear();
      _phoneController.clear();
      setState(() {
        _selectedGender = '男';
        _agreeTerms = false;
      });
    }
  }

  // 构建数据组件展示区域的辅助方法
  Widget _buildDataSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget content, // 要展示的内容Widget
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            content, // 显示传入的内容Widget
          ],
        ),
      ),
    );
  }
}
