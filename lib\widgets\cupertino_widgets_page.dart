import 'package:flutter/cupertino.dart'; // 导入Flutter的Cupertino组件库

// Cupertino风格组件展示页面
class CupertinoWidgetsPage extends StatefulWidget {
  const CupertinoWidgetsPage({super.key}); // 构造函数，接收可选的key参数

  @override
  State<CupertinoWidgetsPage> createState() => _CupertinoWidgetsPageState();
}

class _CupertinoWidgetsPageState extends State<CupertinoWidgetsPage> {
  bool _switchValue = false; // Switch开关状态
  double _sliderValue = 0.5; // Slider滑块值
  int _segmentedValue = 0; // SegmentedControl选中值
  DateTime _selectedDate = DateTime.now(); // 选中的日期
  String _selectedItem = 'Apple'; // Picker选中的项目

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      navigationBar: const CupertinoNavigationBar(
        middle: Text('Cupertino组件'), // 导航栏标题
        backgroundColor: CupertinoColors.systemBackground, // 导航栏背景色
      ), // iOS风格导航栏
      child: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
          children: [
            // Cupertino按钮示例
            _buildCupertinoSection(
              context,
              'Cupertino Buttons - iOS风格按钮',
              '各种iOS风格的按钮组件',
              Column(
                children: [
                  // 填充按钮
                  CupertinoButton.filled(
                    onPressed: () {
                      _showCupertinoAlert(context, '点击了填充按钮');
                    },
                    child: const Text('填充按钮'), // 按钮文本
                  ),
                  const SizedBox(height: 12), // 垂直间距12像素
                  // 普通按钮
                  CupertinoButton(
                    color: CupertinoColors.activeBlue, // 蓝色背景
                    onPressed: () {
                      _showCupertinoAlert(context, '点击了蓝色按钮');
                    },
                    child: const Text('蓝色按钮'), // 按钮文本
                  ),
                  const SizedBox(height: 12), // 垂直间距12像素
                  // 文本按钮
                  CupertinoButton(
                    onPressed: () {
                      _showCupertinoAlert(context, '点击了文本按钮');
                    },
                    child: const Text('文本按钮'), // 按钮文本
                  ),
                  const SizedBox(height: 12), // 垂直间距12像素
                  // 带图标的按钮
                  CupertinoButton(
                    color: CupertinoColors.systemGreen, // 绿色背景
                    onPressed: () {
                      _showCupertinoAlert(context, '点击了图标按钮');
                    },
                    child: const Row(
                      mainAxisSize: MainAxisSize.min, // 主轴尺寸最小
                      children: [
                        Icon(
                          CupertinoIcons.heart_fill,
                          color: CupertinoColors.white,
                        ), // 心形图标
                        SizedBox(width: 8), // 水平间距8像素
                        Text('喜欢'), // 按钮文本
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20), // 垂直间距20像素
            // Cupertino输入框示例
            _buildCupertinoSection(
              context,
              'Cupertino TextFields - iOS风格输入框',
              '各种iOS风格的文本输入框',
              Column(
                children: [
                  // 标准输入框
                  const CupertinoTextField(
                    placeholder: '请输入用户名', // 占位符文本
                    prefix: Icon(
                      CupertinoIcons.person,
                      color: CupertinoColors.systemGrey,
                    ), // 前缀图标
                    decoration: BoxDecoration(
                      border: Border.fromBorderSide(
                        BorderSide(color: CupertinoColors.systemGrey4),
                      ), // 边框样式
                      borderRadius: BorderRadius.all(
                        Radius.circular(8),
                      ), // 圆角边框
                    ),
                    padding: EdgeInsets.all(12), // 内边距12像素
                  ),
                  const SizedBox(height: 12), // 垂直间距12像素
                  // 密码输入框
                  const CupertinoTextField(
                    placeholder: '请输入密码', // 占位符文本
                    obscureText: true, // 密码模式
                    prefix: Icon(
                      CupertinoIcons.lock,
                      color: CupertinoColors.systemGrey,
                    ), // 前缀图标
                    suffix: Icon(
                      CupertinoIcons.eye,
                      color: CupertinoColors.systemGrey,
                    ), // 后缀图标
                    decoration: BoxDecoration(
                      border: Border.fromBorderSide(
                        BorderSide(color: CupertinoColors.systemGrey4),
                      ), // 边框样式
                      borderRadius: BorderRadius.all(
                        Radius.circular(8),
                      ), // 圆角边框
                    ),
                    padding: EdgeInsets.all(12), // 内边距12像素
                  ),
                  const SizedBox(height: 12), // 垂直间距12像素
                  // 搜索框
                  const CupertinoTextField(
                    placeholder: '搜索...', // 占位符文本
                    prefix: Icon(
                      CupertinoIcons.search,
                      color: CupertinoColors.systemGrey,
                    ), // 前缀图标
                    decoration: BoxDecoration(
                      color: CupertinoColors.systemGrey6, // 背景色
                      borderRadius: BorderRadius.all(
                        Radius.circular(20),
                      ), // 圆角边框
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ), // 内边距
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20), // 垂直间距20像素
            // Cupertino开关和滑块示例
            _buildCupertinoSection(
              context,
              'Cupertino Controls - iOS风格控件',
              '开关、滑块等iOS风格控件',
              Column(
                children: [
                  // 开关控件
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween, // 主轴两端对齐
                    children: [
                      const Text('启用通知'), // 标签文本
                      CupertinoSwitch(
                        value: _switchValue, // 开关当前值
                        onChanged: (bool value) {
                          setState(() {
                            _switchValue = value; // 更新开关状态
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 20), // 垂直间距20像素
                  // 滑块控件
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                    children: [
                      Text('音量: ${(_sliderValue * 100).round()}%'), // 显示滑块值
                      const SizedBox(height: 8), // 垂直间距8像素
                      CupertinoSlider(
                        value: _sliderValue, // 滑块当前值
                        onChanged: (double value) {
                          setState(() {
                            _sliderValue = value; // 更新滑块值
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20), // 垂直间距20像素
            // Cupertino分段控件示例
            _buildCupertinoSection(
              context,
              'Cupertino SegmentedControl - 分段控件',
              'iOS风格的分段选择控件',
              Column(
                children: [
                  CupertinoSegmentedControl<int>(
                    children: const {
                      0: Padding(
                        padding: EdgeInsets.all(8), // 内边距8像素
                        child: Text('第一个'), // 分段文本
                      ),
                      1: Padding(
                        padding: EdgeInsets.all(8), // 内边距8像素
                        child: Text('第二个'), // 分段文本
                      ),
                      2: Padding(
                        padding: EdgeInsets.all(8), // 内边距8像素
                        child: Text('第三个'), // 分段文本
                      ),
                    },
                    groupValue: _segmentedValue, // 当前选中值
                    onValueChanged: (int value) {
                      setState(() {
                        _segmentedValue = value; // 更新选中值
                      });
                    },
                  ),
                  const SizedBox(height: 16), // 垂直间距16像素
                  Text('当前选中: ${_segmentedValue + 1}'), // 显示选中项
                ],
              ),
            ),

            const SizedBox(height: 20), // 垂直间距20像素
            // Cupertino日期选择器示例
            _buildCupertinoSection(
              context,
              'Cupertino DatePicker - 日期选择器',
              'iOS风格的日期时间选择器',
              Column(
                children: [
                  Container(
                    height: 200, // 容器高度200像素
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: CupertinoColors.systemGrey4,
                      ), // 边框
                      borderRadius: BorderRadius.circular(8), // 圆角边框
                    ),
                    child: CupertinoDatePicker(
                      mode: CupertinoDatePickerMode.date, // 日期模式
                      initialDateTime: _selectedDate, // 初始日期
                      onDateTimeChanged: (DateTime newDate) {
                        setState(() {
                          _selectedDate = newDate; // 更新选中日期
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 12), // 垂直间距12像素
                  Text(
                    '选中日期: ${_selectedDate.year}-${_selectedDate.month.toString().padLeft(2, '0')}-${_selectedDate.day.toString().padLeft(2, '0')}',
                  ), // 显示选中日期
                ],
              ),
            ),

            const SizedBox(height: 20), // 垂直间距20像素
            // Cupertino选择器示例
            _buildCupertinoSection(
              context,
              'Cupertino Picker - 选择器',
              'iOS风格的滚轮选择器',
              Column(
                children: [
                  Container(
                    height: 150, // 容器高度150像素
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: CupertinoColors.systemGrey4,
                      ), // 边框
                      borderRadius: BorderRadius.circular(8), // 圆角边框
                    ),
                    child: CupertinoPicker(
                      itemExtent: 32, // 每项高度32像素
                      onSelectedItemChanged: (int index) {
                        setState(() {
                          _selectedItem =
                              [
                                'Apple',
                                'Banana',
                                'Orange',
                                'Grape',
                                'Watermelon',
                              ][index]; // 更新选中项
                        });
                      },
                      children: const [
                        Center(child: Text('Apple')), // 选项1
                        Center(child: Text('Banana')), // 选项2
                        Center(child: Text('Orange')), // 选项3
                        Center(child: Text('Grape')), // 选项4
                        Center(child: Text('Watermelon')), // 选项5
                      ],
                    ),
                  ),
                  const SizedBox(height: 12), // 垂直间距12像素
                  Text('选中水果: $_selectedItem'), // 显示选中项
                ],
              ),
            ),

            const SizedBox(height: 20), // 垂直间距20像素
            // Cupertino活动指示器示例
            _buildCupertinoSection(
              context,
              'Cupertino ActivityIndicator - 活动指示器',
              'iOS风格的加载指示器',
              const Column(
                children: [
                  CupertinoActivityIndicator(
                    radius: 20, // 半径20像素
                  ),
                  SizedBox(height: 16), // 垂直间距16像素
                  Text('加载中...'), // 提示文本
                ],
              ),
            ),
          ],
        ),
      ), // 页面主体内容
    );
  }

  // 显示Cupertino风格弹窗的辅助方法
  void _showCupertinoAlert(BuildContext context, String message) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: const Text('提示'), // 弹窗标题
          content: Text(message), // 弹窗内容
          actions: [
            CupertinoDialogAction(
              child: const Text('确定'), // 按钮文本
              onPressed: () {
                Navigator.of(context).pop(); // 关闭弹窗
              },
            ),
          ],
        );
      },
    );
  }

  // 构建Cupertino组件展示区域的辅助方法
  Widget _buildCupertinoSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget content, // 要展示的内容Widget
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16), // 底部外边距16像素
      padding: const EdgeInsets.all(16), // 内边距四周各16像素
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground, // 系统背景色
        borderRadius: BorderRadius.circular(12), // 圆角半径12像素
        boxShadow: [
          BoxShadow(
            color: CupertinoColors.systemGrey.withValues(alpha: 0.3), // 半透明灰色阴影
            blurRadius: 8, // 阴影模糊半径8像素
            offset: const Offset(0, 2), // 阴影偏移量
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
        children: [
          Text(
            title, // 显示标题
            style: const TextStyle(
              fontSize: 18, // 字体大小18
              fontWeight: FontWeight.bold, // 字体加粗
              color: CupertinoColors.label, // 标签颜色
            ),
          ),
          const SizedBox(height: 8), // 垂直间距8像素
          Text(
            description, // 显示描述
            style: const TextStyle(
              fontSize: 14, // 字体大小14
              color: CupertinoColors.secondaryLabel, // 次要标签颜色
            ),
          ),
          const SizedBox(height: 16), // 垂直间距16像素
          content, // 显示传入的内容Widget
        ],
      ),
    );
  }
}
