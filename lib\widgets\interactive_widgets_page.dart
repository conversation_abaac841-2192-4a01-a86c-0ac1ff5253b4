import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库

// 交互Widget展示页面
class InteractiveWidgetsPage extends StatefulWidget {
  const InteractiveWidgetsPage({super.key}); // 构造函数，接收可选的key参数

  @override
  State<InteractiveWidgetsPage> createState() => _InteractiveWidgetsPageState();
}

class _InteractiveWidgetsPageState extends State<InteractiveWidgetsPage> {
  // 状态变量
  bool _switchValue = false; // Switch开关状态
  bool _checkboxValue = false; // Checkbox复选框状态
  String _radioValue = 'option1'; // Radio单选按钮值
  double _sliderValue = 50.0; // Slider滑块值
  final TextEditingController _textController =
      TextEditingController(); // 文本输入控制器

  @override
  void dispose() {
    _textController.dispose(); // 释放文本控制器资源
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('交互组件'), // AppBar标题
        backgroundColor:
            Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // Button Widget 示例
          _buildWidgetSection(context, 'Button Widgets - 按钮组件', '各种类型的按钮组件', [
            ElevatedButton(
              onPressed: () {
                // 按钮点击事件处理
                _showSnackBar('点击了 ElevatedButton');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue, // 按钮背景色蓝色
                foregroundColor: Colors.white, // 按钮文字颜色白色
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 12,
                ), // 内边距
              ), // 按钮样式设置
              child: const Text('ElevatedButton'), // 按钮文本
            ),
            OutlinedButton(
              onPressed: () {
                _showSnackBar('点击了 OutlinedButton');
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.green, // 按钮文字和边框颜色绿色
                side: const BorderSide(color: Colors.green, width: 2), // 边框样式
              ), // 按钮样式设置
              child: const Text('OutlinedButton'), // 按钮文本
            ),
            TextButton(
              onPressed: () {
                _showSnackBar('点击了 TextButton');
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.purple, // 按钮文字颜色紫色
              ), // 按钮样式设置
              child: const Text('TextButton'), // 按钮文本
            ),
            IconButton(
              onPressed: () {
                _showSnackBar('点击了 IconButton');
              },
              icon: const Icon(Icons.favorite), // 按钮图标
              color: Colors.red, // 图标颜色红色
              iconSize: 30, // 图标大小30像素
            ),
          ]),

          const SizedBox(height: 20), // 垂直间距20像素
          // TextField Widget 示例
          _buildWidgetSection(
            context,
            'TextField Widget - 文本输入框',
            '用于接收用户文本输入的组件',
            [
              TextField(
                controller: _textController, // 绑定文本控制器
                decoration: const InputDecoration(
                  labelText: '请输入文本', // 标签文本
                  hintText: '这里是提示文本', // 提示文本
                  border: OutlineInputBorder(), // 边框样式
                  prefixIcon: Icon(Icons.edit), // 前缀图标
                ), // 输入框装饰
                onChanged: (value) {
                  // 文本变化时的回调
                  // 在实际应用中，这里可以处理文本变化逻辑
                },
              ),
              const SizedBox(height: 10), // 垂直间距10像素
              TextField(
                obscureText: true, // 密码输入模式，隐藏文本
                decoration: const InputDecoration(
                  labelText: '密码', // 标签文本
                  border: OutlineInputBorder(), // 边框样式
                  prefixIcon: Icon(Icons.lock), // 前缀图标
                  suffixIcon: Icon(Icons.visibility), // 后缀图标
                ), // 输入框装饰
              ),
            ],
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // Switch Widget 示例
          _buildWidgetSection(context, 'Switch Widget - 开关组件', '用于切换开/关状态的组件', [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween, // 主轴两端对齐
              children: [
                Text('开关状态: ${_switchValue ? "开启" : "关闭"}'), // 显示开关状态
                Switch(
                  value: _switchValue, // 开关当前值
                  onChanged: (bool value) {
                    // 开关状态改变时的回调
                    setState(() {
                      _switchValue = value; // 更新状态
                    });
                    _showSnackBar('开关${value ? "开启" : "关闭"}');
                  },
                  activeColor: Colors.green, // 开启时的颜色
                  inactiveThumbColor: Colors.grey, // 关闭时滑块颜色
                ),
              ],
            ),
          ]),

          const SizedBox(height: 20), // 垂直间距20像素
          // Checkbox Widget 示例
          _buildWidgetSection(
            context,
            'Checkbox Widget - 复选框组件',
            '用于多选的复选框组件',
            [
              Row(
                children: [
                  Checkbox(
                    value: _checkboxValue, // 复选框当前值
                    onChanged: (bool? value) {
                      // 复选框状态改变时的回调
                      setState(() {
                        _checkboxValue = value ?? false; // 更新状态，空值时默认为false
                      });
                      _showSnackBar('复选框${value! ? "选中" : "取消选中"}');
                    },
                    activeColor: Colors.blue, // 选中时的颜色
                  ),
                  Text('复选框状态: ${_checkboxValue ? "选中" : "未选中"}'), // 显示复选框状态
                ],
              ),
            ],
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // Radio Widget 示例
          _buildWidgetSection(context, 'Radio Widget - 单选按钮组件', '用于单选的单选按钮组件', [
            Column(
              children: [
                RadioListTile<String>(
                  title: const Text('选项 1'), // 单选项标题
                  value: 'option1', // 单选项值
                  groupValue: _radioValue, // 当前选中的值
                  onChanged: (String? value) {
                    // 单选按钮状态改变时的回调
                    setState(() {
                      _radioValue = value!; // 更新选中值
                    });
                    _showSnackBar('选择了选项 1');
                  },
                  activeColor: Colors.orange, // 选中时的颜色
                ),
                RadioListTile<String>(
                  title: const Text('选项 2'), // 单选项标题
                  value: 'option2', // 单选项值
                  groupValue: _radioValue, // 当前选中的值
                  onChanged: (String? value) {
                    setState(() {
                      _radioValue = value!;
                    });
                    _showSnackBar('选择了选项 2');
                  },
                  activeColor: Colors.orange, // 选中时的颜色
                ),
              ],
            ),
          ]),

          const SizedBox(height: 20), // 垂直间距20像素
          // Slider Widget 示例
          _buildWidgetSection(
            context,
            'Slider Widget - 滑块组件',
            '用于选择数值范围的滑块组件',
            [
              Column(
                children: [
                  Text('滑块值: ${_sliderValue.round()}'), // 显示滑块当前值（四舍五入）
                  Slider(
                    value: _sliderValue, // 滑块当前值
                    min: 0.0, // 最小值
                    max: 100.0, // 最大值
                    divisions: 10, // 分割数量，创建10个刻度
                    label: _sliderValue.round().toString(), // 滑块标签显示当前值
                    onChanged: (double value) {
                      // 滑块值改变时的回调
                      setState(() {
                        _sliderValue = value; // 更新滑块值
                      });
                    },
                    onChangeEnd: (double value) {
                      // 滑块拖拽结束时的回调
                      _showSnackBar('滑块最终值: ${value.round()}');
                    },
                    activeColor: Colors.purple, // 激活部分的颜色
                    inactiveColor: Colors.purple.withValues(
                      alpha: 0.3,
                    ), // 非激活部分的颜色
                  ),
                ],
              ),
            ],
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 显示SnackBar的辅助方法
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message), // SnackBar显示的消息
        duration: const Duration(seconds: 2), // 显示时长2秒
        backgroundColor: Theme.of(context).colorScheme.primary, // 背景色使用主题主色
      ),
    );
  }

  // 构建Widget展示区域的辅助方法
  Widget _buildWidgetSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    List<Widget> widgets, // 要展示的Widget列表
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            ...widgets, // 展开显示传入的Widget列表
          ],
        ),
      ),
    );
  }
}
