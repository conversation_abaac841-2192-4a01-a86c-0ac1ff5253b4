import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库
import 'package:flutter/cupertino.dart'; // 导入Flutter的Cupertino组件库

// 主题和样式Widget展示页面
class ThemeWidgetsPage extends StatefulWidget {
  const ThemeWidgetsPage({super.key}); // 构造函数，接收可选的key参数

  @override
  State<ThemeWidgetsPage> createState() => _ThemeWidgetsPageState();
}

class _ThemeWidgetsPageState extends State<ThemeWidgetsPage> {
  bool _isDarkMode = false; // 是否为深色模式
  bool _isCupertinoStyle = false; // 是否为Cupertino风格

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('主题和样式'), // AppBar标题
        backgroundColor:
            Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
        actions: [
          // 主题切换按钮
          IconButton(
            icon: Icon(
              _isDarkMode ? Icons.light_mode : Icons.dark_mode,
            ), // 根据当前模式显示对应图标
            onPressed: () {
              setState(() {
                _isDarkMode = !_isDarkMode; // 切换深色模式状态
              });
            },
            tooltip: _isDarkMode ? '切换到浅色模式' : '切换到深色模式', // 工具提示
          ),
        ],
      ), // 页面顶部导航栏
      body: Theme(
        data:
            _isDarkMode
                ? ThemeData.dark(useMaterial3: true) // 深色主题，使用Material 3
                : ThemeData.light(useMaterial3: true), // 浅色主题，使用Material 3
        child: Container(
          color: _isDarkMode ? Colors.grey[900] : Colors.grey[50], // 根据模式设置背景色
          child: ListView(
            padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
            children: [
              // 主题切换控制区域
              _buildThemeSection(
                context,
                '主题控制',
                '切换不同的主题和样式风格',
                Column(
                  children: [
                    SwitchListTile(
                      title: const Text('深色模式'), // 开关标题
                      subtitle: const Text('切换浅色/深色主题'), // 开关副标题
                      value: _isDarkMode, // 开关当前值
                      onChanged: (bool value) {
                        setState(() {
                          _isDarkMode = value; // 更新深色模式状态
                        });
                      },
                      secondary: Icon(
                        _isDarkMode ? Icons.dark_mode : Icons.light_mode,
                      ), // 开关前导图标
                    ),
                    SwitchListTile(
                      title: const Text('Cupertino风格'), // 开关标题
                      subtitle: const Text('切换Material/Cupertino风格'), // 开关副标题
                      value: _isCupertinoStyle, // 开关当前值
                      onChanged: (bool value) {
                        setState(() {
                          _isCupertinoStyle = value; // 更新Cupertino风格状态
                        });
                      },
                      secondary: Icon(
                        _isCupertinoStyle ? Icons.phone_iphone : Icons.android,
                      ), // 开关前导图标
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20), // 垂直间距20像素
              // 颜色主题展示
              _buildThemeSection(
                context,
                '颜色主题',
                '展示当前主题的颜色方案',
                Wrap(
                  spacing: 12, // 水平间距12像素
                  runSpacing: 12, // 垂直间距12像素
                  children: [
                    _buildColorCard(
                      '主色',
                      Theme.of(context).colorScheme.primary,
                    ),
                    _buildColorCard(
                      '次要色',
                      Theme.of(context).colorScheme.secondary,
                    ),
                    _buildColorCard(
                      '表面色',
                      Theme.of(context).colorScheme.surface,
                    ),
                    _buildColorCard(
                      '表面色2',
                      Theme.of(context).colorScheme.surface,
                    ),
                    _buildColorCard('错误色', Theme.of(context).colorScheme.error),
                    _buildColorCard(
                      '轮廓色',
                      Theme.of(context).colorScheme.outline,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20), // 垂直间距20像素
              // 文字样式展示
              _buildThemeSection(
                context,
                '文字样式',
                '展示不同的文字主题样式',
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                  children: [
                    Text(
                      '大标题 (headlineLarge)',
                      style: Theme.of(context).textTheme.headlineLarge, // 大标题样式
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    Text(
                      '中标题 (headlineMedium)',
                      style:
                          Theme.of(context).textTheme.headlineMedium, // 中标题样式
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    Text(
                      '小标题 (headlineSmall)',
                      style: Theme.of(context).textTheme.headlineSmall, // 小标题样式
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    Text(
                      '标题大 (titleLarge)',
                      style: Theme.of(context).textTheme.titleLarge, // 大标题样式
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    Text(
                      '标题中 (titleMedium)',
                      style: Theme.of(context).textTheme.titleMedium, // 中标题样式
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    Text(
                      '正文大 (bodyLarge)',
                      style: Theme.of(context).textTheme.bodyLarge, // 大正文样式
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    Text(
                      '正文中 (bodyMedium)',
                      style: Theme.of(context).textTheme.bodyMedium, // 中正文样式
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    Text(
                      '正文小 (bodySmall)',
                      style: Theme.of(context).textTheme.bodySmall, // 小正文样式
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20), // 垂直间距20像素
              // 按钮样式展示
              _buildThemeSection(
                context,
                '按钮样式',
                '展示不同风格的按钮组件',
                Column(
                  children: [
                    // Material Design 按钮
                    if (!_isCupertinoStyle) ...[
                      const Text(
                        'Material Design 按钮',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12), // 垂直间距12像素
                      Wrap(
                        spacing: 12, // 水平间距12像素
                        runSpacing: 12, // 垂直间距12像素
                        children: [
                          ElevatedButton(
                            onPressed: () {}, // 空的点击事件
                            child: const Text('ElevatedButton'),
                          ),
                          FilledButton(
                            onPressed: () {}, // 空的点击事件
                            child: const Text('FilledButton'),
                          ),
                          OutlinedButton(
                            onPressed: () {}, // 空的点击事件
                            child: const Text('OutlinedButton'),
                          ),
                          TextButton(
                            onPressed: () {}, // 空的点击事件
                            child: const Text('TextButton'),
                          ),
                        ],
                      ),
                    ],
                    // Cupertino 按钮
                    if (_isCupertinoStyle) ...[
                      const Text(
                        'Cupertino 按钮',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12), // 垂直间距12像素
                      Wrap(
                        spacing: 12, // 水平间距12像素
                        runSpacing: 12, // 垂直间距12像素
                        children: [
                          CupertinoButton(
                            color: CupertinoColors.activeBlue, // 蓝色背景
                            onPressed: () {}, // 空的点击事件
                            child: const Text('CupertinoButton'),
                          ),
                          CupertinoButton.filled(
                            onPressed: () {}, // 空的点击事件
                            child: const Text('Filled Button'),
                          ),
                          CupertinoButton(
                            onPressed: () {}, // 空的点击事件
                            child: const Text('Text Button'),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 20), // 垂直间距20像素
              // 输入框样式展示
              _buildThemeSection(
                context,
                '输入框样式',
                '展示不同风格的输入框组件',
                Column(
                  children: [
                    // Material Design 输入框
                    if (!_isCupertinoStyle) ...[
                      const Text(
                        'Material Design 输入框',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12), // 垂直间距12像素
                      const TextField(
                        decoration: InputDecoration(
                          labelText: '标准输入框', // 标签文本
                          hintText: '请输入内容', // 提示文本
                          border: OutlineInputBorder(), // 边框样式
                          prefixIcon: Icon(Icons.person), // 前缀图标
                        ),
                      ),
                      const SizedBox(height: 12), // 垂直间距12像素
                      const TextField(
                        decoration: InputDecoration(
                          labelText: '填充输入框', // 标签文本
                          hintText: '请输入内容', // 提示文本
                          filled: true, // 填充背景
                          border: UnderlineInputBorder(), // 下划线边框
                          prefixIcon: Icon(Icons.email), // 前缀图标
                        ),
                      ),
                    ],
                    // Cupertino 输入框
                    if (_isCupertinoStyle) ...[
                      const Text(
                        'Cupertino 输入框',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12), // 垂直间距12像素
                      const CupertinoTextField(
                        placeholder: 'Cupertino输入框', // 占位符文本
                        prefix: Icon(CupertinoIcons.person), // 前缀图标
                        decoration: BoxDecoration(
                          border: Border.fromBorderSide(
                            BorderSide(color: CupertinoColors.systemGrey4),
                          ), // 边框样式
                          borderRadius: BorderRadius.all(
                            Radius.circular(8),
                          ), // 圆角边框
                        ),
                      ),
                      const SizedBox(height: 12), // 垂直间距12像素
                      const CupertinoTextField(
                        placeholder: '搜索框样式', // 占位符文本
                        prefix: Icon(CupertinoIcons.search), // 前缀图标
                        decoration: BoxDecoration(
                          color: CupertinoColors.systemGrey6, // 背景色
                          borderRadius: BorderRadius.all(
                            Radius.circular(20),
                          ), // 圆角边框
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              const SizedBox(height: 20), // 垂直间距20像素
              // 卡片和容器样式
              _buildThemeSection(
                context,
                '卡片和容器',
                '展示不同的卡片和容器样式',
                Column(
                  children: [
                    Card(
                      elevation: 4, // 卡片阴影高度4像素
                      child: Padding(
                        padding: const EdgeInsets.all(16), // 内边距16像素
                        child: Column(
                          crossAxisAlignment:
                              CrossAxisAlignment.start, // 子组件左对齐
                          children: [
                            Text(
                              '标准卡片',
                              style:
                                  Theme.of(
                                    context,
                                  ).textTheme.titleMedium, // 中标题样式
                            ),
                            const SizedBox(height: 8), // 垂直间距8像素
                            Text(
                              '这是一个使用当前主题样式的标准卡片组件',
                              style:
                                  Theme.of(
                                    context,
                                  ).textTheme.bodyMedium, // 中正文样式
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 12), // 垂直间距12像素
                    Container(
                      width: double.infinity, // 宽度填满父容器
                      padding: const EdgeInsets.all(16), // 内边距16像素
                      decoration: BoxDecoration(
                        color:
                            Theme.of(
                              context,
                            ).colorScheme.primaryContainer, // 主色容器背景
                        borderRadius: BorderRadius.circular(12), // 圆角半径12像素
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline, // 轮廓色边框
                          width: 1, // 边框宽度1像素
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                        children: [
                          Text(
                            '主题容器',
                            style: Theme.of(
                              context,
                            ).textTheme.titleMedium?.copyWith(
                              color:
                                  Theme.of(context)
                                      .colorScheme
                                      .onPrimaryContainer, // 主色容器上的文字颜色
                            ),
                          ),
                          const SizedBox(height: 8), // 垂直间距8像素
                          Text(
                            '这个容器使用了主题的主色容器背景和对应的文字颜色',
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              color:
                                  Theme.of(context)
                                      .colorScheme
                                      .onPrimaryContainer, // 主色容器上的文字颜色
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ), // 页面主体内容
    );
  }

  // 构建主题展示区域的辅助方法
  Widget _buildThemeSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget content, // 要展示的内容Widget
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            content, // 显示传入的内容Widget
          ],
        ),
      ),
    );
  }

  // 构建颜色卡片的辅助方法
  Widget _buildColorCard(String name, Color color) {
    return Container(
      width: 80, // 容器宽度80像素
      height: 80, // 容器高度80像素
      decoration: BoxDecoration(
        color: color, // 背景颜色
        borderRadius: BorderRadius.circular(8), // 圆角半径8像素
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.3), // 半透明灰色边框
          width: 1, // 边框宽度1像素
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center, // 主轴居中对齐
        children: [
          Text(
            name, // 颜色名称
            style: TextStyle(
              color: _getContrastColor(color), // 根据背景色选择对比色文字
              fontSize: 12, // 字体大小12
              fontWeight: FontWeight.bold, // 字体加粗
            ),
            textAlign: TextAlign.center, // 文本居中对齐
          ),
        ],
      ),
    );
  }

  // 根据背景色获取对比色的辅助方法
  Color _getContrastColor(Color backgroundColor) {
    // 计算亮度，如果背景色较暗则返回白色，否则返回黑色
    double luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}
