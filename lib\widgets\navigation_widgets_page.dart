import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库

// 导航组件展示页面
class NavigationWidgetsPage extends StatefulWidget {
  const NavigationWidgetsPage({super.key}); // 构造函数，接收可选的key参数

  @override
  State<NavigationWidgetsPage> createState() => _NavigationWidgetsPageState();
}

class _NavigationWidgetsPageState extends State<NavigationWidgetsPage>
    with TickerProviderStateMixin { // 提供动画Ticker
  
  int _currentBottomIndex = 0; // 底部导航当前索引
  late TabController _tabController; // Tab控制器

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this); // 初始化Tab控制器，3个标签页
  }

  @override
  void dispose() {
    _tabController.dispose(); // 释放Tab控制器资源
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('导航组件'), // AppBar标题
        backgroundColor: Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
        actions: [
          // AppBar操作按钮
          IconButton(
            icon: const Icon(Icons.search), // 搜索图标
            onPressed: () {
              _showSnackBar('点击了搜索按钮');
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_vert), // 更多图标
            onPressed: () {
              _showSnackBar('点击了更多按钮');
            },
          ),
        ],
      ), // 页面顶部导航栏
      drawer: _buildDrawer(), // 侧边抽屉
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // AppBar 应用栏示例
          _buildNavigationSection(
            context,
            'AppBar - 应用栏',
            '页面顶部的导航栏，支持标题、操作按钮等',
            Card(
              child: Column(
                children: [
                  AppBar(
                    title: const Text('示例AppBar'), // 标题
                    backgroundColor: Colors.blue, // 背景色
                    foregroundColor: Colors.white, // 前景色
                    leading: const Icon(Icons.menu), // 前导图标
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.favorite), // 喜欢图标
                        onPressed: () {},
                      ),
                      IconButton(
                        icon: const Icon(Icons.share), // 分享图标
                        onPressed: () {},
                      ),
                    ],
                    elevation: 4, // 阴影高度
                  ),
                  const Padding(
                    padding: EdgeInsets.all(16), // 内边距16像素
                    child: Text('这是一个示例AppBar，展示了标题、图标和操作按钮'),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // TabBar 标签栏示例
          _buildNavigationSection(
            context,
            'TabBar - 标签栏',
            '水平标签导航，支持滑动切换',
            Card(
              child: Column(
                children: [
                  TabBar(
                    controller: _tabController, // 绑定控制器
                    labelColor: Theme.of(context).colorScheme.primary, // 选中标签颜色
                    unselectedLabelColor: Colors.grey, // 未选中标签颜色
                    indicatorColor: Theme.of(context).colorScheme.primary, // 指示器颜色
                    tabs: const [
                      Tab(
                        icon: Icon(Icons.home), // 标签图标
                        text: '首页', // 标签文本
                      ),
                      Tab(
                        icon: Icon(Icons.business), // 标签图标
                        text: '商务', // 标签文本
                      ),
                      Tab(
                        icon: Icon(Icons.school), // 标签图标
                        text: '学校', // 标签文本
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 200, // 容器高度200像素
                    child: TabBarView(
                      controller: _tabController, // 绑定控制器
                      children: [
                        _buildTabContent('首页内容', Icons.home, Colors.blue),
                        _buildTabContent('商务内容', Icons.business, Colors.green),
                        _buildTabContent('学校内容', Icons.school, Colors.orange),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // BottomNavigationBar 底部导航示例
          _buildNavigationSection(
            context,
            'BottomNavigationBar - 底部导航',
            '页面底部的导航栏，支持多个标签页',
            Card(
              child: Column(
                children: [
                  Container(
                    height: 200, // 容器高度200像素
                    padding: const EdgeInsets.all(20), // 内边距20像素
                    child: _buildBottomNavContent(_currentBottomIndex),
                  ),
                  BottomNavigationBar(
                    currentIndex: _currentBottomIndex, // 当前选中索引
                    onTap: (int index) {
                      setState(() {
                        _currentBottomIndex = index; // 更新选中索引
                      });
                    },
                    type: BottomNavigationBarType.fixed, // 固定类型
                    selectedItemColor: Theme.of(context).colorScheme.primary, // 选中项颜色
                    unselectedItemColor: Colors.grey, // 未选中项颜色
                    items: const [
                      BottomNavigationBarItem(
                        icon: Icon(Icons.home), // 图标
                        label: '首页', // 标签
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.search), // 图标
                        label: '搜索', // 标签
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.favorite), // 图标
                        label: '收藏', // 标签
                      ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.person), // 图标
                        label: '我的', // 标签
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // NavigationRail 导航栏示例
          _buildNavigationSection(
            context,
            'NavigationRail - 侧边导航栏',
            '垂直的侧边导航栏，适合宽屏设备',
            Card(
              child: SizedBox(
                height: 300, // 容器高度300像素
                child: Row(
                  children: [
                    NavigationRail(
                      selectedIndex: 0, // 选中索引
                      onDestinationSelected: (int index) {
                        _showSnackBar('选择了导航项 ${index + 1}');
                      },
                      labelType: NavigationRailLabelType.all, // 显示所有标签
                      destinations: const [
                        NavigationRailDestination(
                          icon: Icon(Icons.home), // 图标
                          label: Text('首页'), // 标签
                        ),
                        NavigationRailDestination(
                          icon: Icon(Icons.bookmark), // 图标
                          label: Text('书签'), // 标签
                        ),
                        NavigationRailDestination(
                          icon: Icon(Icons.star), // 图标
                          label: Text('收藏'), // 标签
                        ),
                        NavigationRailDestination(
                          icon: Icon(Icons.download), // 图标
                          label: Text('下载'), // 标签
                        ),
                      ],
                    ),
                    const VerticalDivider(thickness: 1, width: 1), // 垂直分割线
                    Expanded(
                      child: Container(
                        color: Colors.grey.withValues(alpha: 0.1), // 半透明灰色背景
                        child: const Center(
                          child: Text(
                            'NavigationRail内容区域\n适合平板和桌面设备',
                            textAlign: TextAlign.center, // 文本居中
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // 路由导航示例
          _buildNavigationSection(
            context,
            'Route Navigation - 路由导航',
            '页面间的跳转和导航管理',
            Column(
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const RouteDetailPage(title: '普通路由页面'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.arrow_forward), // 按钮图标
                  label: const Text('普通路由跳转'), // 按钮文本
                ),
                const SizedBox(height: 12), // 垂直间距12像素
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      PageRouteBuilder(
                        pageBuilder: (context, animation, secondaryAnimation) =>
                            const RouteDetailPage(title: '自定义动画路由'),
                        transitionsBuilder: (context, animation, secondaryAnimation, child) {
                          const begin = Offset(1.0, 0.0); // 动画开始位置
                          const end = Offset.zero; // 动画结束位置
                          const curve = Curves.ease; // 动画曲线

                          var tween = Tween(begin: begin, end: end).chain(
                            CurveTween(curve: curve),
                          );

                          return SlideTransition(
                            position: animation.drive(tween), // 滑动动画
                            child: child,
                          );
                        },
                      ),
                    );
                  },
                  icon: const Icon(Icons.animation), // 按钮图标
                  label: const Text('自定义动画路由'), // 按钮文本
                ),
                const SizedBox(height: 12), // 垂直间距12像素
                ElevatedButton.icon(
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      builder: (BuildContext context) {
                        return Container(
                          height: 200, // 容器高度200像素
                          padding: const EdgeInsets.all(20), // 内边距20像素
                          child: Column(
                            children: [
                              const Text(
                                'Modal Bottom Sheet',
                                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(height: 16), // 垂直间距16像素
                              const Text('这是一个模态底部弹出页面'),
                              const SizedBox(height: 16), // 垂直间距16像素
                              ElevatedButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('关闭'),
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  },
                  icon: const Icon(Icons.vertical_align_bottom), // 按钮图标
                  label: const Text('底部弹出页面'), // 按钮文本
                ),
              ],
            ),
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 构建侧边抽屉的方法
  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero, // 无内边距
        children: [
          const DrawerHeader(
            decoration: BoxDecoration(
              color: Colors.blue, // 头部背景色
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
              children: [
                CircleAvatar(
                  radius: 30, // 头像半径30像素
                  backgroundColor: Colors.white, // 头像背景色
                  child: Icon(Icons.person, size: 40, color: Colors.blue), // 头像图标
                ),
                SizedBox(height: 10), // 垂直间距10像素
                Text(
                  '用户名',
                  style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Text(
                  '<EMAIL>',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ],
            ),
          ), // 抽屉头部
          ListTile(
            leading: const Icon(Icons.home), // 前导图标
            title: const Text('首页'), // 标题
            onTap: () {
              Navigator.pop(context); // 关闭抽屉
              _showSnackBar('点击了首页');
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings), // 前导图标
            title: const Text('设置'), // 标题
            onTap: () {
              Navigator.pop(context); // 关闭抽屉
              _showSnackBar('点击了设置');
            },
          ),
          ListTile(
            leading: const Icon(Icons.help), // 前导图标
            title: const Text('帮助'), // 标题
            onTap: () {
              Navigator.pop(context); // 关闭抽屉
              _showSnackBar('点击了帮助');
            },
          ),
          const Divider(), // 分割线
          ListTile(
            leading: const Icon(Icons.logout), // 前导图标
            title: const Text('退出登录'), // 标题
            onTap: () {
              Navigator.pop(context); // 关闭抽屉
              _showSnackBar('点击了退出登录');
            },
          ),
        ],
      ),
    );
  }

  // 构建Tab内容的方法
  Widget _buildTabContent(String title, IconData icon, Color color) {
    return Container(
      color: color.withValues(alpha: 0.1), // 半透明背景色
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center, // 主轴居中对齐
          children: [
            Icon(icon, size: 48, color: color), // 图标
            const SizedBox(height: 16), // 垂直间距16像素
            Text(
              title, // 标题
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: color),
            ),
          ],
        ),
      ),
    );
  }

  // 构建底部导航内容的方法
  Widget _buildBottomNavContent(int index) {
    final List<Map<String, dynamic>> contents = [
      {'title': '首页内容', 'icon': Icons.home, 'color': Colors.blue},
      {'title': '搜索内容', 'icon': Icons.search, 'color': Colors.green},
      {'title': '收藏内容', 'icon': Icons.favorite, 'color': Colors.red},
      {'title': '我的内容', 'icon': Icons.person, 'color': Colors.purple},
    ];

    final content = contents[index];
    return Container(
      color: (content['color'] as Color).withValues(alpha: 0.1), // 半透明背景色
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center, // 主轴居中对齐
          children: [
            Icon(content['icon'], size: 48, color: content['color']), // 图标
            const SizedBox(height: 16), // 垂直间距16像素
            Text(
              content['title'], // 标题
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: content['color']),
            ),
          ],
        ),
      ),
    );
  }

  // 显示SnackBar的辅助方法
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message), // SnackBar显示的消息
        duration: const Duration(seconds: 2), // 显示时长2秒
      ),
    );
  }

  // 构建导航组件展示区域的辅助方法
  Widget _buildNavigationSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget content, // 要展示的内容Widget
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            content, // 显示传入的内容Widget
          ],
        ),
      ),
    );
  }
}

// 路由详情页面
class RouteDetailPage extends StatelessWidget {
  final String title; // 页面标题

  const RouteDetailPage({super.key, required this.title}); // 构造函数

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title), // 显示传入的标题
        backgroundColor: Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
      ), // 页面顶部导航栏
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center, // 主轴居中对齐
          children: [
            Icon(
              Icons.check_circle, // 成功图标
              size: 80, // 图标大小80像素
              color: Colors.green, // 绿色图标
            ),
            const SizedBox(height: 24), // 垂直间距24像素
            Text(
              title, // 显示标题
              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            const Text(
              '这是一个路由跳转的示例页面',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 32), // 垂直间距32像素
            ElevatedButton(
              onPressed: () => Navigator.pop(context), // 返回上一页
              child: const Text('返回上一页'),
            ),
          ],
        ),
      ), // 页面主体内容
    );
  }
}
