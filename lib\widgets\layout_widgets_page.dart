import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库

// 布局Widget展示页面
class LayoutWidgetsPage extends StatelessWidget {
  const LayoutWidgetsPage({super.key}); // 构造函数，接收可选的key参数

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('布局组件'), // AppBar标题
        backgroundColor:
            Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // Row Widget 示例
          _buildWidgetSection(context, 'Row Widget - 水平布局', '将子组件水平排列的布局组件', [
            Container(
              height: 80, // 容器高度80像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly, // 主轴均匀分布
                crossAxisAlignment: CrossAxisAlignment.center, // 交叉轴居中
                children: [
                  Container(
                    width: 50, // 宽度50像素
                    height: 50, // 高度50像素
                    color: Colors.red, // 红色背景
                    child: const Center(
                      child: Text('1', style: TextStyle(color: Colors.white)),
                    ),
                  ),
                  Container(
                    width: 50,
                    height: 50,
                    color: Colors.green, // 绿色背景
                    child: const Center(
                      child: Text('2', style: TextStyle(color: Colors.white)),
                    ),
                  ),
                  Container(
                    width: 50,
                    height: 50,
                    color: Colors.blue, // 蓝色背景
                    child: const Center(
                      child: Text('3', style: TextStyle(color: Colors.white)),
                    ),
                  ),
                ],
              ),
            ),
          ]),

          const SizedBox(height: 20), // 垂直间距20像素
          // Column Widget 示例
          _buildWidgetSection(
            context,
            'Column Widget - 垂直布局',
            '将子组件垂直排列的布局组件',
            [
              Container(
                width: 200, // 容器宽度200像素
                height: 200, // 容器高度200像素
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey), // 灰色边框
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround, // 主轴均匀分布
                  crossAxisAlignment: CrossAxisAlignment.center, // 交叉轴居中
                  children: [
                    Container(
                      width: 80, // 宽度80像素
                      height: 40, // 高度40像素
                      color: Colors.purple, // 紫色背景
                      child: const Center(
                        child: Text('A', style: TextStyle(color: Colors.white)),
                      ),
                    ),
                    Container(
                      width: 80,
                      height: 40,
                      color: Colors.orange, // 橙色背景
                      child: const Center(
                        child: Text('B', style: TextStyle(color: Colors.white)),
                      ),
                    ),
                    Container(
                      width: 80,
                      height: 40,
                      color: Colors.teal, // 青色背景
                      child: const Center(
                        child: Text('C', style: TextStyle(color: Colors.white)),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // Stack Widget 示例
          _buildWidgetSection(context, 'Stack Widget - 层叠布局', '将子组件层叠放置的布局组件', [
            Container(
              width: 200, // 容器宽度200像素
              height: 150, // 容器高度150像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
              ),
              child: Stack(
                children: [
                  // 底层容器
                  Container(
                    width: 120, // 宽度120像素
                    height: 120, // 高度120像素
                    color: Colors.blue.withValues(alpha: 0.7), // 半透明蓝色
                    child: const Center(
                      child: Text(
                        '底层',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                      ),
                    ),
                  ),
                  // 中层容器
                  Positioned(
                    top: 30, // 距离顶部30像素
                    left: 40, // 距离左边40像素
                    child: Container(
                      width: 100, // 宽度100像素
                      height: 80, // 高度80像素
                      color: Colors.red.withValues(alpha: 0.7), // 半透明红色
                      child: const Center(
                        child: Text(
                          '中层',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),
                    ),
                  ),
                  // 顶层容器
                  Positioned(
                    top: 60, // 距离顶部60像素
                    left: 80, // 距离左边80像素
                    child: Container(
                      width: 80, // 宽度80像素
                      height: 60, // 高度60像素
                      color: Colors.green.withValues(alpha: 0.7), // 半透明绿色
                      child: const Center(
                        child: Text(
                          '顶层',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ]),

          const SizedBox(height: 20), // 垂直间距20像素
          // Flex Widget 示例
          _buildWidgetSection(context, 'Flex Widget - 弹性布局', '可以设置方向的弹性布局组件', [
            Container(
              height: 100, // 容器高度100像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
              ),
              child: Flex(
                direction: Axis.horizontal, // 水平方向布局
                children: [
                  Expanded(
                    flex: 1, // 占用1份空间
                    child: Container(
                      color: Colors.red, // 红色背景
                      child: const Center(
                        child: Text(
                          '1份',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2, // 占用2份空间
                    child: Container(
                      color: Colors.green, // 绿色背景
                      child: const Center(
                        child: Text(
                          '2份',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3, // 占用3份空间
                    child: Container(
                      color: Colors.blue, // 蓝色背景
                      child: const Center(
                        child: Text(
                          '3份',
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ]),

          const SizedBox(height: 20), // 垂直间距20像素
          // Wrap Widget 示例
          _buildWidgetSection(
            context,
            'Wrap Widget - 流式布局',
            '当空间不足时自动换行的布局组件',
            [
              Container(
                width: double.infinity, // 宽度填满父容器
                padding: const EdgeInsets.all(8), // 内边距8像素
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey), // 灰色边框
                ),
                child: Wrap(
                  spacing: 8, // 水平间距8像素
                  runSpacing: 8, // 垂直间距8像素
                  children: List.generate(
                    10, // 生成10个子组件
                    (index) => Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ), // 水平12像素，垂直6像素内边距
                      decoration: BoxDecoration(
                        color:
                            Colors.primaries[index %
                                Colors.primaries.length], // 循环使用主色
                        borderRadius: BorderRadius.circular(15), // 圆角半径15像素
                      ),
                      child: Text(
                        '标签 ${index + 1}', // 标签文本
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 构建Widget展示区域的辅助方法
  Widget _buildWidgetSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    List<Widget> widgets, // 要展示的Widget列表
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            ...widgets, // 展开显示传入的Widget列表
          ],
        ),
      ),
    );
  }
}
