import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库

// 高级布局组件展示页面
class AdvancedLayoutPage extends StatelessWidget {
  const AdvancedLayoutPage({super.key}); // 构造函数，接收可选的key参数

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('高级布局组件'), // AppBar标题
        backgroundColor: Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // LinearLayout 线性布局示例
          _buildLayoutSection(
            context,
            'LinearLayout - 线性布局',
            '类似Android的LinearLayout，支持权重分配',
            Container(
              height: 120, // 容器高度120像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
              ),
              child: Column(
                children: [
                  // 水平线性布局
                  Expanded(
                    flex: 1, // 占用1份空间
                    child: Row(
                      children: [
                        Expanded(
                          flex: 1, // 权重1
                          child: Container(
                            color: Colors.red.withValues(alpha: 0.7), // 半透明红色
                            child: const Center(
                              child: Text('权重1', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2, // 权重2
                          child: Container(
                            color: Colors.green.withValues(alpha: 0.7), // 半透明绿色
                            child: const Center(
                              child: Text('权重2', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3, // 权重3
                          child: Container(
                            color: Colors.blue.withValues(alpha: 0.7), // 半透明蓝色
                            child: const Center(
                              child: Text('权重3', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 垂直线性布局
                  Expanded(
                    flex: 1, // 占用1份空间
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              Expanded(
                                flex: 2, // 权重2
                                child: Container(
                                  color: Colors.purple.withValues(alpha: 0.7), // 半透明紫色
                                  child: const Center(
                                    child: Text('上部2', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                  ),
                                ),
                              ),
                              Expanded(
                                flex: 1, // 权重1
                                child: Container(
                                  color: Colors.orange.withValues(alpha: 0.7), // 半透明橙色
                                  child: const Center(
                                    child: Text('下部1', style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // FlexBox 弹性布局示例
          _buildLayoutSection(
            context,
            'FlexBox - 弹性布局',
            '灵活的弹性布局，支持主轴和交叉轴对齐',
            Column(
              children: [
                // 主轴对齐方式展示
                Container(
                  height: 80, // 容器高度80像素
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey), // 灰色边框
                    borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start, // 交叉轴左对齐
                    children: [
                      const Padding(
                        padding: EdgeInsets.all(4), // 内边距4像素
                        child: Text('MainAxisAlignment.spaceEvenly', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
                      ),
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly, // 主轴均匀分布
                          children: [
                            _buildFlexItem('A', Colors.red),
                            _buildFlexItem('B', Colors.green),
                            _buildFlexItem('C', Colors.blue),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12), // 垂直间距12像素
                Container(
                  height: 80, // 容器高度80像素
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey), // 灰色边框
                    borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start, // 交叉轴左对齐
                    children: [
                      const Padding(
                        padding: EdgeInsets.all(4), // 内边距4像素
                        child: Text('MainAxisAlignment.spaceBetween', style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold)),
                      ),
                      Expanded(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween, // 主轴两端对齐
                          children: [
                            _buildFlexItem('X', Colors.purple),
                            _buildFlexItem('Y', Colors.orange),
                            _buildFlexItem('Z', Colors.teal),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // Flow Layout 流式布局示例
          _buildLayoutSection(
            context,
            'Flow Layout - 流式布局',
            '自动换行的流式布局，适合标签展示',
            Container(
              width: double.infinity, // 宽度填满父容器
              padding: const EdgeInsets.all(12), // 内边距12像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                color: Colors.grey.withValues(alpha: 0.1), // 半透明灰色背景
              ),
              child: Wrap(
                spacing: 8, // 水平间距8像素
                runSpacing: 8, // 垂直间距8像素
                children: [
                  _buildTag('Flutter', Colors.blue),
                  _buildTag('Dart', Colors.green),
                  _buildTag('Widget', Colors.purple),
                  _buildTag('Layout', Colors.orange),
                  _buildTag('Material Design', Colors.red),
                  _buildTag('Cupertino', Colors.grey),
                  _buildTag('Animation', Colors.pink),
                  _buildTag('State Management', Colors.teal),
                  _buildTag('Navigation', Colors.indigo),
                  _buildTag('Responsive', Colors.amber),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // Stack Layout 层叠布局示例
          _buildLayoutSection(
            context,
            'Stack Layout - 层叠布局',
            '支持绝对定位的层叠布局',
            Container(
              height: 200, // 容器高度200像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
              ),
              child: Stack(
                children: [
                  // 背景层
                  Container(
                    width: double.infinity, // 宽度填满父容器
                    height: double.infinity, // 高度填满父容器
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.blue.withValues(alpha: 0.3), Colors.purple.withValues(alpha: 0.3)], // 渐变背景
                        begin: Alignment.topLeft, // 渐变开始位置
                        end: Alignment.bottomRight, // 渐变结束位置
                      ),
                      borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                    ),
                  ),
                  // 左上角元素
                  Positioned(
                    top: 10, // 距离顶部10像素
                    left: 10, // 距离左边10像素
                    child: Container(
                      width: 60, // 宽度60像素
                      height: 40, // 高度40像素
                      decoration: BoxDecoration(
                        color: Colors.red, // 红色背景
                        borderRadius: BorderRadius.circular(6), // 圆角半径6像素
                      ),
                      child: const Center(
                        child: Text('左上', style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold)),
                      ),
                    ),
                  ),
                  // 右上角元素
                  Positioned(
                    top: 10, // 距离顶部10像素
                    right: 10, // 距离右边10像素
                    child: Container(
                      width: 60, // 宽度60像素
                      height: 40, // 高度40像素
                      decoration: BoxDecoration(
                        color: Colors.green, // 绿色背景
                        borderRadius: BorderRadius.circular(6), // 圆角半径6像素
                      ),
                      child: const Center(
                        child: Text('右上', style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold)),
                      ),
                    ),
                  ),
                  // 中心元素
                  Center(
                    child: Container(
                      width: 80, // 宽度80像素
                      height: 80, // 高度80像素
                      decoration: BoxDecoration(
                        color: Colors.orange, // 橙色背景
                        shape: BoxShape.circle, // 圆形形状
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.3), // 半透明黑色阴影
                            blurRadius: 8, // 阴影模糊半径8像素
                            offset: const Offset(0, 4), // 阴影偏移量
                          ),
                        ],
                      ),
                      child: const Center(
                        child: Text('中心', style: TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold)),
                      ),
                    ),
                  ),
                  // 左下角元素
                  Positioned(
                    bottom: 10, // 距离底部10像素
                    left: 10, // 距离左边10像素
                    child: Container(
                      width: 60, // 宽度60像素
                      height: 40, // 高度40像素
                      decoration: BoxDecoration(
                        color: Colors.purple, // 紫色背景
                        borderRadius: BorderRadius.circular(6), // 圆角半径6像素
                      ),
                      child: const Center(
                        child: Text('左下', style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold)),
                      ),
                    ),
                  ),
                  // 右下角元素
                  Positioned(
                    bottom: 10, // 距离底部10像素
                    right: 10, // 距离右边10像素
                    child: Container(
                      width: 60, // 宽度60像素
                      height: 40, // 高度40像素
                      decoration: BoxDecoration(
                        color: Colors.teal, // 青色背景
                        borderRadius: BorderRadius.circular(6), // 圆角半径6像素
                      ),
                      child: const Center(
                        child: Text('右下', style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold)),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // Table Layout 表格布局示例
          _buildLayoutSection(
            context,
            'Table Layout - 表格布局',
            '规整的表格布局，适合数据展示',
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
              ),
              child: Table(
                border: TableBorder.all(color: Colors.grey.withValues(alpha: 0.5)), // 表格边框
                children: [
                  // 表头
                  TableRow(
                    decoration: BoxDecoration(color: Colors.blue.withValues(alpha: 0.1)), // 表头背景色
                    children: [
                      _buildTableCell('姓名', isHeader: true),
                      _buildTableCell('年龄', isHeader: true),
                      _buildTableCell('职业', isHeader: true),
                    ],
                  ),
                  // 数据行1
                  TableRow(
                    children: [
                      _buildTableCell('张三'),
                      _buildTableCell('25'),
                      _buildTableCell('工程师'),
                    ],
                  ),
                  // 数据行2
                  TableRow(
                    children: [
                      _buildTableCell('李四'),
                      _buildTableCell('30'),
                      _buildTableCell('设计师'),
                    ],
                  ),
                  // 数据行3
                  TableRow(
                    children: [
                      _buildTableCell('王五'),
                      _buildTableCell('28'),
                      _buildTableCell('产品经理'),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 构建布局展示区域的辅助方法
  Widget _buildLayoutSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget layoutWidget, // 要展示的布局Widget
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            layoutWidget, // 显示传入的布局Widget
          ],
        ),
      ),
    );
  }

  // 构建弹性布局项的辅助方法
  Widget _buildFlexItem(String text, Color color) {
    return Container(
      width: 40, // 宽度40像素
      height: 40, // 高度40像素
      decoration: BoxDecoration(
        color: color, // 背景颜色
        borderRadius: BorderRadius.circular(6), // 圆角半径6像素
      ),
      child: Center(
        child: Text(
          text, // 显示文本
          style: const TextStyle(
            color: Colors.white, // 白色文字
            fontWeight: FontWeight.bold, // 字体加粗
          ),
        ),
      ),
    );
  }

  // 构建标签的辅助方法
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6), // 水平12像素，垂直6像素内边距
      decoration: BoxDecoration(
        color: color, // 背景颜色
        borderRadius: BorderRadius.circular(16), // 圆角半径16像素
      ),
      child: Text(
        text, // 显示文本
        style: const TextStyle(
          color: Colors.white, // 白色文字
          fontSize: 12, // 字体大小12
          fontWeight: FontWeight.bold, // 字体加粗
        ),
      ),
    );
  }

  // 构建表格单元格的辅助方法
  Widget _buildTableCell(String text, {bool isHeader = false}) {
    return Padding(
      padding: const EdgeInsets.all(8), // 内边距8像素
      child: Text(
        text, // 显示文本
        textAlign: TextAlign.center, // 文本居中对齐
        style: TextStyle(
          fontWeight: isHeader ? FontWeight.bold : FontWeight.normal, // 表头字体加粗
          fontSize: isHeader ? 14 : 12, // 表头字体稍大
        ),
      ),
    );
  }
}
