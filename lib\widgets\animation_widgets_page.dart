import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库

// 动画Widget展示页面
class AnimationWidgetsPage extends StatefulWidget {
  const AnimationWidgetsPage({super.key}); // 构造函数，接收可选的key参数

  @override
  State<AnimationWidgetsPage> createState() => _AnimationWidgetsPageState();
}

class _AnimationWidgetsPageState extends State<AnimationWidgetsPage>
    with TickerProviderStateMixin { // 提供动画Ticker
  
  // 动画控制器
  late AnimationController _rotationController; // 旋转动画控制器
  late AnimationController _scaleController; // 缩放动画控制器
  late AnimationController _fadeController; // 淡入淡出动画控制器
  late AnimationController _slideController; // 滑动动画控制器
  
  // 动画对象
  late Animation<double> _rotationAnimation; // 旋转动画
  late Animation<double> _scaleAnimation; // 缩放动画
  late Animation<double> _fadeAnimation; // 淡入淡出动画
  late Animation<Offset> _slideAnimation; // 滑动动画

  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2), // 动画持续时间2秒
      vsync: this, // 垂直同步信号提供者
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800), // 动画持续时间800毫秒
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000), // 动画持续时间1000毫秒
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600), // 动画持续时间600毫秒
      vsync: this,
    );
    
    // 初始化动画对象
    _rotationAnimation = Tween<double>(
      begin: 0.0, // 开始值
      end: 2.0, // 结束值（2π弧度，即360度）
    ).animate(CurvedAnimation(
      parent: _rotationController, // 父动画控制器
      curve: Curves.linear, // 线性动画曲线
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.5, // 开始缩放比例
      end: 1.5, // 结束缩放比例
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut, // 弹性输出曲线
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0, // 完全透明
      end: 1.0, // 完全不透明
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut, // 缓入缓出曲线
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0), // 从左侧开始
      end: Offset.zero, // 滑动到原位置
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.bounceOut, // 弹跳输出曲线
    ));
  }

  @override
  void dispose() {
    // 释放动画控制器资源
    _rotationController.dispose();
    _scaleController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('动画组件'), // AppBar标题
        backgroundColor: Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // 旋转动画示例
          _buildAnimationSection(
            context,
            '旋转动画 - RotationTransition',
            '使用RotationTransition实现旋转动画效果',
            Column(
              children: [
                AnimatedBuilder(
                  animation: _rotationAnimation, // 绑定旋转动画
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _rotationAnimation.value * 3.14159, // 旋转角度（弧度）
                      child: Container(
                        width: 80, // 容器宽度80像素
                        height: 80, // 容器高度80像素
                        decoration: BoxDecoration(
                          color: Colors.blue, // 蓝色背景
                          borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                        ),
                        child: const Icon(
                          Icons.refresh, // 刷新图标
                          color: Colors.white, // 白色图标
                          size: 40, // 图标大小40像素
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16), // 垂直间距16像素
                ElevatedButton(
                  onPressed: () {
                    // 开始或停止旋转动画
                    if (_rotationController.isAnimating) {
                      _rotationController.stop(); // 停止动画
                    } else {
                      _rotationController.repeat(); // 重复播放动画
                    }
                  },
                  child: Text(_rotationController.isAnimating ? '停止旋转' : '开始旋转'),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // 缩放动画示例
          _buildAnimationSection(
            context,
            '缩放动画 - ScaleTransition',
            '使用ScaleTransition实现缩放动画效果',
            Column(
              children: [
                AnimatedBuilder(
                  animation: _scaleAnimation, // 绑定缩放动画
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimation.value, // 缩放比例
                      child: Container(
                        width: 60, // 容器宽度60像素
                        height: 60, // 容器高度60像素
                        decoration: BoxDecoration(
                          color: Colors.green, // 绿色背景
                          shape: BoxShape.circle, // 圆形形状
                        ),
                        child: const Icon(
                          Icons.favorite, // 喜欢图标
                          color: Colors.white, // 白色图标
                          size: 30, // 图标大小30像素
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16), // 垂直间距16像素
                ElevatedButton(
                  onPressed: () {
                    // 播放缩放动画
                    _scaleController.forward().then((_) {
                      _scaleController.reverse(); // 动画完成后反向播放
                    });
                  },
                  child: const Text('播放缩放动画'),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // 淡入淡出动画示例
          _buildAnimationSection(
            context,
            '淡入淡出动画 - FadeTransition',
            '使用FadeTransition实现透明度动画效果',
            Column(
              children: [
                AnimatedBuilder(
                  animation: _fadeAnimation, // 绑定淡入淡出动画
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeAnimation.value, // 透明度值
                      child: Container(
                        width: 100, // 容器宽度100像素
                        height: 100, // 容器高度100像素
                        decoration: BoxDecoration(
                          color: Colors.purple, // 紫色背景
                          borderRadius: BorderRadius.circular(12), // 圆角半径12像素
                        ),
                        child: const Center(
                          child: Text(
                            '淡入淡出',
                            style: TextStyle(
                              color: Colors.white, // 白色文字
                              fontWeight: FontWeight.bold, // 字体加粗
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 16), // 垂直间距16像素
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly, // 主轴均匀分布
                  children: [
                    ElevatedButton(
                      onPressed: () => _fadeController.forward(), // 淡入
                      child: const Text('淡入'),
                    ),
                    ElevatedButton(
                      onPressed: () => _fadeController.reverse(), // 淡出
                      child: const Text('淡出'),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // 滑动动画示例
          _buildAnimationSection(
            context,
            '滑动动画 - SlideTransition',
            '使用SlideTransition实现位移动画效果',
            Column(
              children: [
                Container(
                  height: 80, // 容器高度80像素
                  width: double.infinity, // 宽度填满父容器
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey), // 灰色边框
                    borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                  ),
                  child: ClipRect( // 裁剪矩形，防止子组件超出边界
                    child: AnimatedBuilder(
                      animation: _slideAnimation, // 绑定滑动动画
                      builder: (context, child) {
                        return SlideTransition(
                          position: _slideAnimation, // 滑动位置
                          child: Container(
                            width: 120, // 容器宽度120像素
                            height: 60, // 容器高度60像素
                            margin: const EdgeInsets.all(10), // 外边距10像素
                            decoration: BoxDecoration(
                              color: Colors.orange, // 橙色背景
                              borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                            ),
                            child: const Center(
                              child: Text(
                                '滑动元素',
                                style: TextStyle(
                                  color: Colors.white, // 白色文字
                                  fontWeight: FontWeight.bold, // 字体加粗
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 16), // 垂直间距16像素
                ElevatedButton(
                  onPressed: () {
                    // 播放滑动动画
                    _slideController.reset(); // 重置动画
                    _slideController.forward(); // 播放动画
                  },
                  child: const Text('播放滑动动画'),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素

          // Hero动画示例
          _buildAnimationSection(
            context,
            'Hero动画 - 页面间共享元素动画',
            '点击图片查看Hero动画效果',
            GestureDetector(
              onTap: () {
                // 导航到Hero动画详情页
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const HeroDetailPage(),
                  ),
                );
              },
              child: Hero(
                tag: 'hero-image', // Hero标签，用于识别共享元素
                child: Container(
                  width: 100, // 容器宽度100像素
                  height: 100, // 容器高度100像素
                  decoration: BoxDecoration(
                    color: Colors.red, // 红色背景
                    borderRadius: BorderRadius.circular(12), // 圆角半径12像素
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.3), // 半透明黑色阴影
                        blurRadius: 8, // 阴影模糊半径8像素
                        offset: const Offset(0, 4), // 阴影偏移量
                      ),
                    ],
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.image, // 图片图标
                      color: Colors.white, // 白色图标
                      size: 50, // 图标大小50像素
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 构建动画展示区域的辅助方法
  Widget _buildAnimationSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget animationWidget, // 要展示的动画Widget
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            Center(child: animationWidget), // 居中显示动画Widget
          ],
        ),
      ),
    );
  }
}

// Hero动画详情页面
class HeroDetailPage extends StatelessWidget {
  const HeroDetailPage({super.key}); // 构造函数，接收可选的key参数

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Hero动画详情'), // AppBar标题
        backgroundColor: Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
      ), // 页面顶部导航栏
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center, // 主轴居中对齐
          children: [
            Hero(
              tag: 'hero-image', // 与前一页面相同的Hero标签
              child: Container(
                width: 200, // 容器宽度200像素（比前一页面大）
                height: 200, // 容器高度200像素
                decoration: BoxDecoration(
                  color: Colors.red, // 红色背景
                  borderRadius: BorderRadius.circular(20), // 圆角半径20像素
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3), // 半透明黑色阴影
                      blurRadius: 16, // 阴影模糊半径16像素
                      offset: const Offset(0, 8), // 阴影偏移量
                    ),
                  ],
                ),
                child: const Center(
                  child: Icon(
                    Icons.image, // 图片图标
                    color: Colors.white, // 白色图标
                    size: 100, // 图标大小100像素（比前一页面大）
                  ),
                ),
              ),
            ),
            const SizedBox(height: 32), // 垂直间距32像素
            const Text(
              'Hero动画演示',
              style: TextStyle(
                fontSize: 24, // 字体大小24
                fontWeight: FontWeight.bold, // 字体加粗
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            const Text(
              '这是Hero动画的详情页面\n图片从小变大的过渡效果就是Hero动画',
              textAlign: TextAlign.center, // 文本居中对齐
              style: TextStyle(fontSize: 16), // 字体大小16
            ),
          ],
        ),
      ), // 页面主体内容
    );
  }
}
