import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库
import 'widgets/basic_widgets_page.dart'; // 导入基础组件页面
import 'widgets/layout_widgets_page.dart'; // 导入布局组件页面
import 'widgets/interactive_widgets_page.dart'; // 导入交互组件页面
import 'widgets/scrollable_widgets_page.dart'; // 导入滚动组件页面
import 'widgets/animation_widgets_page.dart'; // 导入动画组件页面
import 'widgets/theme_widgets_page.dart'; // 导入主题组件页面

void main() {
  runApp(const MyApp()); // 启动Flutter应用，MyApp是根Widget
}

class MyApp extends StatelessWidget {
  const MyApp({super.key}); // 构造函数，super.key用于Widget树的优化

  // 这个widget是应用程序的根widget
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Flutter Widget 教学项目', // 应用标题，显示在任务管理器中
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.deepPurple,
        ), // 从种子颜色生成完整的颜色方案
        useMaterial3: true, // 启用Material 3设计规范
      ), // 应用的主题配置，定义整体的颜色、字体等样式
      home: const WidgetShowcaseHome(), // 应用的主页面Widget
      debugShowCheckedModeBanner: false, // 是否显示右上角的"DEBUG"调试标记
    );
  }
}

// Flutter Widget 教学项目主页面
class WidgetShowcaseHome extends StatelessWidget {
  const WidgetShowcaseHome({super.key}); // 构造函数，接收可选的key参数

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor:
            Theme.of(context).colorScheme.inversePrimary, // 使用主题的反色作为背景
        title: const Text('Flutter Widget 教学'), // AppBar标题文本
        centerTitle: true, // 标题居中显示
        elevation: 4, // AppBar的阴影高度
      ), // 应用顶部导航栏
      body: Padding(
        padding: const EdgeInsets.all(16.0), // 内边距，四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch, // 子组件在交叉轴上拉伸填满
          children: [
            // 欢迎卡片
            Card(
              elevation: 8, // 卡片阴影高度
              margin: const EdgeInsets.only(bottom: 20), // 卡片外边距，仅底部20像素
              child: Padding(
                padding: const EdgeInsets.all(20), // 卡片内边距
                child: Column(
                  children: [
                    Icon(
                      Icons.widgets, // 使用widgets图标
                      size: 64, // 图标大小64像素
                      color: Theme.of(context).colorScheme.primary, // 使用主题主色
                    ),
                    const SizedBox(height: 16), // 垂直间距16像素
                    Text(
                      '欢迎来到 Flutter Widget 学习中心',
                      style:
                          Theme.of(
                            context,
                          ).textTheme.headlineSmall, // 使用主题的小标题样式
                      textAlign: TextAlign.center, // 文本居中对齐
                    ),
                    const SizedBox(height: 8), // 垂直间距8像素
                    Text(
                      '这里包含了Flutter中各种Widget的详细示例和说明',
                      style:
                          Theme.of(context).textTheme.bodyMedium, // 使用主题的正文样式
                      textAlign: TextAlign.center, // 文本居中对齐
                    ),
                  ],
                ),
              ),
            ),
            // Widget分类列表
            Expanded(
              // 占用剩余空间
              child: GridView.count(
                crossAxisCount: 2, // 网格列数为2
                crossAxisSpacing: 16, // 列间距16像素
                mainAxisSpacing: 16, // 行间距16像素
                childAspectRatio: 0.9, // 子组件宽高比，调整为0.9使卡片稍高一些
                children: [
                  _buildCategoryCard(
                    context,
                    '基础组件',
                    Icons.foundation,
                    Colors.blue,
                    '学习Text、Container等基础Widget',
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const BasicWidgetsPage(),
                      ),
                    ),
                  ),
                  _buildCategoryCard(
                    context,
                    '布局组件',
                    Icons.view_quilt,
                    Colors.green,
                    '学习Row、Column、Stack等布局Widget',
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const LayoutWidgetsPage(),
                      ),
                    ),
                  ),
                  _buildCategoryCard(
                    context,
                    '交互组件',
                    Icons.touch_app,
                    Colors.orange,
                    '学习Button、TextField等交互Widget',
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const InteractiveWidgetsPage(),
                      ),
                    ),
                  ),
                  _buildCategoryCard(
                    context,
                    '滚动组件',
                    Icons.view_list,
                    Colors.purple,
                    '学习ListView、GridView等滚动Widget',
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ScrollableWidgetsPage(),
                      ),
                    ),
                  ),
                  _buildCategoryCard(
                    context,
                    '动画组件',
                    Icons.animation,
                    Colors.pink,
                    '学习动画、过渡效果等动画Widget',
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AnimationWidgetsPage(),
                      ),
                    ),
                  ),
                  _buildCategoryCard(
                    context,
                    '主题样式',
                    Icons.palette,
                    Colors.teal,
                    '学习主题切换、颜色方案等样式Widget',
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ThemeWidgetsPage(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ), // 页面主体内容
    );
  }

  // 构建分类卡片的辅助方法
  Widget _buildCategoryCard(
    BuildContext context,
    String title, // 卡片标题
    IconData icon, // 卡片图标
    Color color, // 卡片颜色
    String description, // 卡片描述
    VoidCallback onTap, // 点击回调函数
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度
      child: InkWell(
        // 可点击的水波纹效果组件
        onTap: onTap, // 执行传入的点击回调函数
        borderRadius: BorderRadius.circular(12), // 圆角半径12像素
        child: Padding(
          padding: const EdgeInsets.all(16), // 内边距16像素
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center, // 主轴居中对齐
            children: [
              Icon(
                icon, // 显示传入的图标
                size: 48, // 图标大小48像素
                color: color, // 图标颜色
              ),
              const SizedBox(height: 12), // 垂直间距12像素
              Text(
                title, // 显示标题
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold, // 字体加粗
                ),
                textAlign: TextAlign.center, // 文本居中
              ),
              const SizedBox(height: 8), // 垂直间距8像素
              Text(
                description, // 显示描述文本
                style: Theme.of(context).textTheme.bodySmall, // 使用小号正文样式
                textAlign: TextAlign.center, // 文本居中
                maxLines: 2, // 最多显示2行
                overflow: TextOverflow.ellipsis, // 超出部分显示省略号
              ),
            ],
          ),
        ),
      ),
    );
  }
}
