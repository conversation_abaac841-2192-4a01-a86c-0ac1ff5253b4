import 'package:flutter/material.dart'; // 导入Flutter的Material Design组件库

// 滚动Widget展示页面
class ScrollableWidgetsPage extends StatelessWidget {
  const ScrollableWidgetsPage({super.key}); // 构造函数，接收可选的key参数

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('滚动组件'), // AppBar标题
        backgroundColor:
            Theme.of(context).colorScheme.inversePrimary, // 背景色使用主题反色
        centerTitle: true, // 标题居中
      ), // 页面顶部导航栏
      body: ListView(
        padding: const EdgeInsets.all(16), // 列表内边距，四周各16像素
        children: [
          // ListView Widget 示例
          _buildWidgetSection(
            context,
            'ListView Widget - 列表视图',
            '可滚动的线性排列组件列表',
            Container(
              height: 200, // 容器高度200像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
              ),
              child: ListView.builder(
                itemCount: 20, // 列表项数量20个
                itemBuilder: (context, index) {
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor:
                          Colors.primaries[index %
                              Colors.primaries.length], // 循环使用主色作为头像背景
                      child: Text('${index + 1}'), // 头像显示序号
                    ), // 列表项前导组件
                    title: Text('列表项 ${index + 1}'), // 列表项标题
                    subtitle: Text('这是第 ${index + 1} 个列表项的描述'), // 列表项副标题
                    trailing: const Icon(Icons.arrow_forward_ios), // 列表项尾随组件
                    onTap: () {
                      // 列表项点击事件
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('点击了列表项 ${index + 1}')),
                      );
                    },
                  );
                },
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // GridView Widget 示例
          _buildWidgetSection(
            context,
            'GridView Widget - 网格视图',
            '可滚动的网格布局组件',
            Container(
              height: 300, // 容器高度300像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
              ),
              child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3, // 网格列数3列
                  crossAxisSpacing: 8, // 列间距8像素
                  mainAxisSpacing: 8, // 行间距8像素
                  childAspectRatio: 1, // 子组件宽高比1:1
                ), // 网格布局代理
                padding: const EdgeInsets.all(8), // 网格内边距8像素
                itemCount: 30, // 网格项数量30个
                itemBuilder: (context, index) {
                  return Container(
                    decoration: BoxDecoration(
                      color: Colors.primaries[index % Colors.primaries.length]
                          .withValues(alpha: 0.7), // 半透明主色背景
                      borderRadius: BorderRadius.circular(8), // 圆角半径8像素
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center, // 主轴居中
                        children: [
                          Icon(
                            Icons.apps, // 应用图标
                            color: Colors.white, // 白色图标
                            size: 24, // 图标大小24像素
                          ),
                          const SizedBox(height: 4), // 垂直间距4像素
                          Text(
                            '${index + 1}', // 显示序号
                            style: const TextStyle(
                              color: Colors.white, // 白色文字
                              fontWeight: FontWeight.bold, // 字体加粗
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // PageView Widget 示例
          _buildWidgetSection(
            context,
            'PageView Widget - 页面视图',
            '可滑动切换的页面组件',
            Container(
              height: 200, // 容器高度200像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
              ),
              child: PageView.builder(
                itemCount: 5, // 页面数量5个
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.all(8), // 外边距8像素
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.primaries[index %
                              Colors.primaries.length], // 渐变起始色
                          Colors.primaries[index % Colors.primaries.length]
                              .withValues(alpha: 0.6), // 渐变结束色
                        ],
                        begin: Alignment.topLeft, // 渐变开始位置
                        end: Alignment.bottomRight, // 渐变结束位置
                      ),
                      borderRadius: BorderRadius.circular(12), // 圆角半径12像素
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center, // 主轴居中
                        children: [
                          Icon(
                            Icons.pages, // 页面图标
                            size: 48, // 图标大小48像素
                            color: Colors.white, // 白色图标
                          ),
                          const SizedBox(height: 16), // 垂直间距16像素
                          Text(
                            '页面 ${index + 1}', // 页面标题
                            style: const TextStyle(
                              fontSize: 24, // 字体大小24
                              fontWeight: FontWeight.bold, // 字体加粗
                              color: Colors.white, // 白色文字
                            ),
                          ),
                          const SizedBox(height: 8), // 垂直间距8像素
                          Text(
                            '左右滑动切换页面', // 提示文本
                            style: const TextStyle(
                              fontSize: 16, // 字体大小16
                              color: Colors.white70, // 半透明白色文字
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // SingleChildScrollView Widget 示例
          _buildWidgetSection(
            context,
            'SingleChildScrollView Widget - 单子组件滚动视图',
            '为单个子组件提供滚动功能',
            Container(
              height: 150, // 容器高度150像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16), // 滚动视图内边距16像素
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
                  children: List.generate(
                    10, // 生成10个文本组件
                    (index) => Padding(
                      padding: const EdgeInsets.only(bottom: 8), // 底部外边距8像素
                      child: Text(
                        '这是可滚动的文本内容 ${index + 1}。当内容超出容器高度时，可以垂直滚动查看更多内容。', // 文本内容
                        style: TextStyle(
                          fontSize: 16, // 字体大小16
                          color: Colors.grey[700], // 深灰色文字
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 20), // 垂直间距20像素
          // CustomScrollView Widget 示例
          _buildWidgetSection(
            context,
            'CustomScrollView Widget - 自定义滚动视图',
            '使用Sliver组件的自定义滚动视图',
            Container(
              height: 300, // 容器高度300像素
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey), // 灰色边框
                borderRadius: BorderRadius.circular(8), // 圆角半径8像素
              ),
              child: CustomScrollView(
                slivers: [
                  // SliverAppBar - 可折叠的应用栏
                  SliverAppBar(
                    expandedHeight: 80, // 展开高度80像素
                    floating: false, // 不浮动
                    pinned: true, // 固定在顶部
                    backgroundColor: Colors.blue, // 背景色蓝色
                    flexibleSpace: const FlexibleSpaceBar(
                      title: Text('可折叠标题'), // 标题文本
                      centerTitle: true, // 标题居中
                    ), // 灵活空间栏
                  ),
                  // SliverList - 滚动列表
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        return ListTile(
                          leading: Icon(
                            Icons.star, // 星星图标
                            color: Colors.orange, // 橙色图标
                          ),
                          title: Text('Sliver 列表项 ${index + 1}'), // 列表项标题
                          subtitle: Text(
                            '这是 Sliver 列表的第 ${index + 1} 项',
                          ), // 列表项副标题
                        );
                      },
                      childCount: 15, // 子组件数量15个
                    ), // Sliver子组件构建代理
                  ),
                ],
              ),
            ),
          ),
        ],
      ), // 页面主体内容
    );
  }

  // 构建Widget展示区域的辅助方法
  Widget _buildWidgetSection(
    BuildContext context,
    String title, // 区域标题
    String description, // 区域描述
    Widget widget, // 要展示的Widget
  ) {
    return Card(
      elevation: 4, // 卡片阴影高度4像素
      margin: const EdgeInsets.only(bottom: 16), // 卡片底部外边距16像素
      child: Padding(
        padding: const EdgeInsets.all(16), // 卡片内边距四周各16像素
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 子组件左对齐
          children: [
            Text(
              title, // 显示标题
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold, // 标题字体加粗
                color: Theme.of(context).colorScheme.primary, // 使用主题主色
              ),
            ),
            const SizedBox(height: 8), // 垂直间距8像素
            Text(
              description, // 显示描述
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600], // 描述文字颜色为灰色
              ),
            ),
            const SizedBox(height: 16), // 垂直间距16像素
            widget, // 显示传入的Widget
          ],
        ),
      ),
    );
  }
}
